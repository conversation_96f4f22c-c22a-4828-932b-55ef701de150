# 🚀 Chrome插件安装指南

## 快速安装步骤

### 1. 准备文件
确保你的项目文件夹包含以下文件：
- ✅ `manifest.json`
- ✅ `popup.html`
- ✅ `popup.css` 
- ✅ `popup.js`
- ✅ `content.js`
- ✅ `background.js`
- ✅ `styles.css`

### 2. 打开Chrome扩展管理页面
1. 打开Chrome浏览器
2. 在地址栏输入：`chrome://extensions/`
3. 按回车键

### 3. 启用开发者模式
1. 在扩展管理页面右上角找到"开发者模式"开关
2. 点击开关，启用开发者模式
3. 页面会显示额外的按钮

### 4. 加载插件
1. 点击"加载已解压的扩展程序"按钮
2. 在文件选择器中，导航到你的项目文件夹
3. 选择包含`manifest.json`的文件夹
4. 点击"选择文件夹"

### 5. 验证安装
1. 插件应该出现在扩展列表中
2. 浏览器工具栏会显示插件图标
3. 访问任意网页，应该能看到右上角的浮动工具栏

## 🔧 故障排除

### 常见问题

**问题1: "清单文件无效"**
- 检查`manifest.json`文件格式是否正确
- 确保所有引用的文件都存在

**问题2: "权限被拒绝"**
- 确保Chrome有读取文件夹的权限
- 尝试将项目文件夹移动到用户目录下

**问题3: "插件无法加载"**
- 检查控制台是否有JavaScript错误
- 确保所有文件路径正确

**问题4: "图标不显示"**
- 这是正常的，因为我们还没有添加图标文件
- 插件功能不受影响

### 调试方法

1. **查看错误信息**
   - 在扩展管理页面点击"错误"按钮
   - 查看详细的错误信息

2. **检查控制台**
   - 右键点击插件图标 → "检查弹出内容"
   - 查看popup的控制台错误

3. **检查内容脚本**
   - 在网页上按F12打开开发者工具
   - 查看Console标签页的错误信息

## 🎯 测试功能

安装成功后，你可以测试以下功能：

### 基础功能测试
1. **弹窗界面**
   - 点击插件图标
   - 确认弹窗正常显示

2. **高亮链接**
   - 在弹窗中点击"高亮所有链接"
   - 页面上的链接应该变成黄色背景

3. **浮动工具栏**
   - 访问任意网页
   - 确认右上角显示工具栏
   - 尝试拖拽工具栏

4. **快捷键**
   - 按`Ctrl + Shift + H`测试高亮功能
   - 按`Ctrl + Shift + T`测试工具栏切换

### 高级功能测试
1. **自定义选择器**
   - 在弹窗中输入CSS选择器（如`h1`）
   - 点击"执行自定义操作"

2. **右键菜单**
   - 在网页上右键点击
   - 查看"网页助手"菜单项

3. **设置保存**
   - 勾选"自动高亮链接"
   - 刷新页面验证设置是否保存

## 📝 更新插件

当你修改代码后：

1. 在扩展管理页面找到你的插件
2. 点击刷新按钮（🔄）
3. 或者点击"重新加载"

## 🚀 下一步

插件安装成功后，你可以：

1. **自定义功能**: 修改JavaScript文件添加新功能
2. **美化界面**: 调整CSS样式
3. **添加图标**: 在`icons/`文件夹中添加PNG图标文件
4. **发布插件**: 打包后上传到Chrome Web Store

---

**祝你使用愉快！** 🎉

如果遇到问题，请检查浏览器控制台的错误信息，或参考Chrome扩展开发文档。
