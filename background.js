// 后台脚本 - Service Worker
console.log('🚀 网页助手后台脚本已启动');

// 插件安装时的初始化
chrome.runtime.onInstalled.addListener((details) => {
    console.log('插件已安装/更新:', details.reason);
    
    if (details.reason === 'install') {
        // 首次安装时的设置
        chrome.storage.local.set({
            autoHighlight: false,
            version: '1.0.0',
            installDate: new Date().toISOString()
        });
        
        // 打开欢迎页面（可选）
        // chrome.tabs.create({ url: 'welcome.html' });
    }
});

// 监听来自content script和popup的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('收到消息:', request);
    
    switch (request.action) {
        case 'getTabInfo':
            handleGetTabInfo(sendResponse);
            return true; // 保持消息通道开放
            
        case 'executeOnAllTabs':
            handleExecuteOnAllTabs(request.data, sendResponse);
            return true;
            
        case 'saveData':
            handleSaveData(request.data, sendResponse);
            return true;
            
        case 'getData':
            handleGetData(request.key, sendResponse);
            return true;
            
        default:
            sendResponse({ error: '未知的操作类型' });
    }
});

// 获取当前标签页信息
async function handleGetTabInfo(sendResponse) {
    try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        sendResponse({
            success: true,
            data: {
                id: tab.id,
                url: tab.url,
                title: tab.title,
                favIconUrl: tab.favIconUrl
            }
        });
    } catch (error) {
        sendResponse({ success: false, error: error.message });
    }
}

// 在所有标签页执行操作
async function handleExecuteOnAllTabs(data, sendResponse) {
    try {
        const tabs = await chrome.tabs.query({});
        const results = [];
        
        for (const tab of tabs) {
            try {
                // 跳过chrome://等特殊页面
                if (tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
                    continue;
                }
                
                const result = await chrome.scripting.executeScript({
                    target: { tabId: tab.id },
                    func: data.func,
                    args: data.args || []
                });
                
                results.push({
                    tabId: tab.id,
                    url: tab.url,
                    result: result[0]?.result
                });
            } catch (error) {
                console.error(`在标签页 ${tab.id} 执行脚本失败:`, error);
            }
        }
        
        sendResponse({ success: true, data: results });
    } catch (error) {
        sendResponse({ success: false, error: error.message });
    }
}

// 保存数据
function handleSaveData(data, sendResponse) {
    chrome.storage.local.set(data, () => {
        if (chrome.runtime.lastError) {
            sendResponse({ success: false, error: chrome.runtime.lastError.message });
        } else {
            sendResponse({ success: true });
        }
    });
}

// 获取数据
function handleGetData(key, sendResponse) {
    chrome.storage.local.get(key, (result) => {
        if (chrome.runtime.lastError) {
            sendResponse({ success: false, error: chrome.runtime.lastError.message });
        } else {
            sendResponse({ success: true, data: result });
        }
    });
}

// 监听标签页更新
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    // 当页面加载完成时
    if (changeInfo.status === 'complete' && tab.url) {
        console.log('页面加载完成:', tab.url);
        
        // 检查是否需要自动执行某些操作
        chrome.storage.local.get(['autoHighlight'], (result) => {
            if (result.autoHighlight) {
                // 自动高亮链接
                chrome.scripting.executeScript({
                    target: { tabId: tabId },
                    func: () => {
                        const links = document.querySelectorAll('a');
                        links.forEach(link => {
                            link.style.backgroundColor = '#ffff00';
                            link.style.border = '2px solid #ff0000';
                            link.style.borderRadius = '3px';
                        });
                    }
                }).catch(error => {
                    console.error('自动高亮失败:', error);
                });
            }
        });
    }
});

// 监听标签页激活
chrome.tabs.onActivated.addListener((activeInfo) => {
    console.log('切换到标签页:', activeInfo.tabId);
});

// 创建右键菜单
chrome.runtime.onInstalled.addListener(() => {
    // 创建主菜单
    chrome.contextMenus.create({
        id: 'webHelper',
        title: '网页助手',
        contexts: ['page', 'selection', 'link', 'image']
    });
    
    // 创建子菜单
    chrome.contextMenus.create({
        id: 'highlightLinks',
        parentId: 'webHelper',
        title: '高亮所有链接',
        contexts: ['page']
    });
    
    chrome.contextMenus.create({
        id: 'hideImages',
        parentId: 'webHelper',
        title: '隐藏所有图片',
        contexts: ['page']
    });
    
    chrome.contextMenus.create({
        id: 'extractText',
        parentId: 'webHelper',
        title: '提取页面文本',
        contexts: ['page', 'selection']
    });
    
    chrome.contextMenus.create({
        id: 'copyLink',
        parentId: 'webHelper',
        title: '复制链接地址',
        contexts: ['link']
    });
});

// 处理右键菜单点击
chrome.contextMenus.onClicked.addListener((info, tab) => {
    switch (info.menuItemId) {
        case 'highlightLinks':
            executeScriptInTab(tab.id, () => {
                const links = document.querySelectorAll('a');
                links.forEach(link => {
                    link.style.backgroundColor = '#ffff00';
                    link.style.border = '2px solid #ff0000';
                    link.style.borderRadius = '3px';
                });
                return links.length;
            });
            break;
            
        case 'hideImages':
            executeScriptInTab(tab.id, () => {
                const images = document.querySelectorAll('img');
                images.forEach(img => img.style.display = 'none');
                return images.length;
            });
            break;
            
        case 'extractText':
            executeScriptInTab(tab.id, () => {
                const text = info.selectionText || document.body.innerText;
                navigator.clipboard.writeText(text);
                return text.length;
            });
            break;
            
        case 'copyLink':
            if (info.linkUrl) {
                // 复制链接到剪贴板
                chrome.scripting.executeScript({
                    target: { tabId: tab.id },
                    func: (url) => {
                        navigator.clipboard.writeText(url);
                    },
                    args: [info.linkUrl]
                });
            }
            break;
    }
});

// 辅助函数：在指定标签页执行脚本
function executeScriptInTab(tabId, func, args = []) {
    chrome.scripting.executeScript({
        target: { tabId: tabId },
        func: func,
        args: args
    }).catch(error => {
        console.error('执行脚本失败:', error);
    });
}

// 监听存储变化
chrome.storage.onChanged.addListener((changes, namespace) => {
    console.log('存储发生变化:', changes, namespace);
    
    // 如果自动高亮设置发生变化，通知所有content scripts
    if (changes.autoHighlight) {
        chrome.tabs.query({}, (tabs) => {
            tabs.forEach(tab => {
                chrome.tabs.sendMessage(tab.id, {
                    action: 'settingChanged',
                    setting: 'autoHighlight',
                    value: changes.autoHighlight.newValue
                }).catch(() => {
                    // 忽略无法发送消息的标签页
                });
            });
        });
    }
});

// 定期清理过期数据（可选）
setInterval(() => {
    chrome.storage.local.get(null, (items) => {
        const now = Date.now();
        const oneWeekAgo = now - (7 * 24 * 60 * 60 * 1000);
        
        Object.keys(items).forEach(key => {
            if (key.startsWith('temp_') && items[key].timestamp < oneWeekAgo) {
                chrome.storage.local.remove(key);
            }
        });
    });
}, 24 * 60 * 60 * 1000); // 每24小时执行一次
