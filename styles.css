/* 网页助手 - 注入到网页中的样式 */

/* 高亮链接的样式 */
.web-helper-highlight-link {
    background-color: #ffff00 !important;
    border: 2px solid #ff0000 !important;
    border-radius: 3px !important;
    transition: all 0.3s ease !important;
}

.web-helper-highlight-link:hover {
    background-color: #ffcc00 !important;
    transform: scale(1.02) !important;
}

/* 隐藏图片的样式 */
.web-helper-hidden-image {
    display: none !important;
}

/* 自定义选择器高亮样式 */
.web-helper-custom-highlight {
    outline: 3px solid #00ff00 !important;
    background-color: rgba(0, 255, 0, 0.1) !important;
    border-radius: 3px !important;
    transition: all 0.3s ease !important;
}

/* 浮动工具栏样式增强 */
#web-helper-toolbar {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
    box-sizing: border-box !important;
}

#web-helper-toolbar * {
    box-sizing: border-box !important;
}

/* 工具栏动画 */
@keyframes webHelperSlideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes webHelperFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes webHelperPulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* 提示消息样式 */
.web-helper-toast {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    background: rgba(0, 0, 0, 0.8) !important;
    color: white !important;
    padding: 12px 16px !important;
    border-radius: 6px !important;
    z-index: 10001 !important;
    font-size: 14px !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    animation: webHelperSlideIn 0.3s ease !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
    max-width: 300px !important;
    word-wrap: break-word !important;
}

.web-helper-toast.success {
    background: rgba(72, 187, 120, 0.9) !important;
}

.web-helper-toast.error {
    background: rgba(245, 101, 101, 0.9) !important;
}

.web-helper-toast.warning {
    background: rgba(237, 137, 54, 0.9) !important;
}

/* 选择器高亮效果 */
.web-helper-selector-highlight {
    position: relative !important;
}

.web-helper-selector-highlight::before {
    content: '' !important;
    position: absolute !important;
    top: -2px !important;
    left: -2px !important;
    right: -2px !important;
    bottom: -2px !important;
    border: 2px solid #00ff00 !important;
    border-radius: 4px !important;
    pointer-events: none !important;
    z-index: 9999 !important;
    animation: webHelperPulse 2s infinite !important;
}

/* 文本提取高亮 */
.web-helper-text-selected {
    background-color: rgba(255, 255, 0, 0.3) !important;
    border-radius: 2px !important;
    transition: background-color 0.3s ease !important;
}

/* 图片替换占位符 */
.web-helper-image-placeholder {
    display: inline-block !important;
    background: #f0f0f0 !important;
    border: 2px dashed #ccc !important;
    border-radius: 4px !important;
    padding: 20px !important;
    text-align: center !important;
    color: #666 !important;
    font-size: 14px !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    min-width: 100px !important;
    min-height: 60px !important;
    position: relative !important;
}

.web-helper-image-placeholder::before {
    content: '🖼️ 图片已隐藏' !important;
}

/* 链接计数器 */
.web-helper-link-counter {
    position: relative !important;
}

.web-helper-link-counter::after {
    content: attr(data-link-number) !important;
    position: absolute !important;
    top: -8px !important;
    right: -8px !important;
    background: #ff4444 !important;
    color: white !important;
    border-radius: 50% !important;
    width: 20px !important;
    height: 20px !important;
    font-size: 12px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-weight: bold !important;
    z-index: 1000 !important;
}

/* 页面遮罩层 */
.web-helper-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0, 0, 0, 0.5) !important;
    z-index: 9998 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    animation: webHelperFadeIn 0.3s ease !important;
}

/* 模态框样式 */
.web-helper-modal {
    background: white !important;
    border-radius: 8px !important;
    padding: 24px !important;
    max-width: 500px !important;
    width: 90% !important;
    max-height: 80vh !important;
    overflow-y: auto !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
    animation: webHelperFadeIn 0.3s ease !important;
}

.web-helper-modal h3 {
    margin: 0 0 16px 0 !important;
    color: #333 !important;
    font-size: 18px !important;
    font-weight: 600 !important;
}

.web-helper-modal p {
    margin: 0 0 16px 0 !important;
    color: #666 !important;
    line-height: 1.5 !important;
}

.web-helper-modal button {
    background: #007bff !important;
    color: white !important;
    border: none !important;
    padding: 8px 16px !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    font-size: 14px !important;
    margin-right: 8px !important;
}

.web-helper-modal button:hover {
    background: #0056b3 !important;
}

.web-helper-modal button.secondary {
    background: #6c757d !important;
}

.web-helper-modal button.secondary:hover {
    background: #545b62 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    #web-helper-toolbar {
        right: 10px !important;
        top: 10px !important;
        min-width: 100px !important;
    }
    
    .web-helper-toast {
        right: 10px !important;
        top: 10px !important;
        max-width: calc(100vw - 20px) !important;
    }
    
    .web-helper-modal {
        width: 95% !important;
        padding: 16px !important;
    }
}

/* 打印时隐藏工具栏 */
@media print {
    #web-helper-toolbar,
    .web-helper-toast,
    .web-helper-overlay,
    .web-helper-modal {
        display: none !important;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .web-helper-highlight-link {
        background-color: #000000 !important;
        color: #ffffff !important;
        border-color: #ffffff !important;
    }
    
    .web-helper-custom-highlight {
        outline-color: #ffffff !important;
        background-color: rgba(255, 255, 255, 0.2) !important;
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
    .web-helper-highlight-link,
    .web-helper-custom-highlight,
    .web-helper-text-selected {
        transition: none !important;
    }
    
    .web-helper-toast,
    .web-helper-overlay,
    .web-helper-modal {
        animation: none !important;
    }
    
    .web-helper-selector-highlight::before {
        animation: none !important;
    }
}
