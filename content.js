// 内容脚本 - 注入到每个网页中
(function() {
    'use strict';

    // 检查是否已经注入过脚本
    if (window.webHelperInjected) {
        return;
    }
    window.webHelperInjected = true;

    console.log('🚀 网页助手已加载');

    // 创建浮动工具栏
    let floatingToolbar = null;
    let isDragging = false;
    let dragOffset = { x: 0, y: 0 };

    // 初始化
    init();

    function init() {
        // 检查自动高亮设置
        chrome.storage.local.get(['autoHighlight'], (result) => {
            if (result.autoHighlight) {
                highlightAllLinks();
            }
        });

        // 创建浮动工具栏
        createFloatingToolbar();

        // 监听来自popup的消息
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            handleMessage(request, sendResponse);
        });

        // 监听键盘快捷键
        document.addEventListener('keydown', handleKeyboardShortcuts);
    }

    // 创建浮动工具栏
    function createFloatingToolbar() {
        floatingToolbar = document.createElement('div');
        floatingToolbar.id = 'web-helper-toolbar';
        floatingToolbar.innerHTML = `
            <div class="toolbar-header">
                <span>🚀</span>
                <button class="close-btn">×</button>
            </div>
            <div class="toolbar-content">
                <button class="tool-btn" data-action="highlight">高亮链接</button>
                <button class="tool-btn" data-action="hide-images">隐藏图片</button>
                <button class="tool-btn" data-action="scroll-top">回到顶部</button>
                <button class="tool-btn" data-action="toggle-toolbar">收起</button>
            </div>
        `;

        // 添加样式
        floatingToolbar.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            min-width: 120px;
            user-select: none;
            cursor: move;
        `;

        document.body.appendChild(floatingToolbar);

        // 添加事件监听器
        setupToolbarEvents();
    }

    // 设置工具栏事件
    function setupToolbarEvents() {
        const header = floatingToolbar.querySelector('.toolbar-header');
        const closeBtn = floatingToolbar.querySelector('.close-btn');
        const toolBtns = floatingToolbar.querySelectorAll('.tool-btn');

        // 拖拽功能
        header.addEventListener('mousedown', startDrag);
        document.addEventListener('mousemove', drag);
        document.addEventListener('mouseup', endDrag);

        // 关闭按钮
        closeBtn.addEventListener('click', () => {
            floatingToolbar.style.display = 'none';
        });

        // 工具按钮
        toolBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.target.dataset.action;
                executeToolbarAction(action);
            });
        });
    }

    // 拖拽相关函数
    function startDrag(e) {
        isDragging = true;
        const rect = floatingToolbar.getBoundingClientRect();
        dragOffset.x = e.clientX - rect.left;
        dragOffset.y = e.clientY - rect.top;
        floatingToolbar.style.cursor = 'grabbing';
    }

    function drag(e) {
        if (!isDragging) return;
        
        const x = e.clientX - dragOffset.x;
        const y = e.clientY - dragOffset.y;
        
        floatingToolbar.style.left = Math.max(0, Math.min(window.innerWidth - floatingToolbar.offsetWidth, x)) + 'px';
        floatingToolbar.style.top = Math.max(0, Math.min(window.innerHeight - floatingToolbar.offsetHeight, y)) + 'px';
        floatingToolbar.style.right = 'auto';
    }

    function endDrag() {
        isDragging = false;
        floatingToolbar.style.cursor = 'move';
    }

    // 执行工具栏操作
    function executeToolbarAction(action) {
        switch (action) {
            case 'highlight':
                highlightAllLinks();
                showToast('已高亮所有链接');
                break;
            case 'hide-images':
                hideAllImages();
                showToast('已隐藏所有图片');
                break;
            case 'scroll-top':
                window.scrollTo({ top: 0, behavior: 'smooth' });
                showToast('已回到顶部');
                break;
            case 'toggle-toolbar':
                toggleToolbar();
                break;
        }
    }

    // 切换工具栏显示状态
    function toggleToolbar() {
        const content = floatingToolbar.querySelector('.toolbar-content');
        const isHidden = content.style.display === 'none';
        content.style.display = isHidden ? 'block' : 'none';
        
        const toggleBtn = floatingToolbar.querySelector('[data-action="toggle-toolbar"]');
        toggleBtn.textContent = isHidden ? '收起' : '展开';
    }

    // 高亮所有链接
    function highlightAllLinks() {
        const links = document.querySelectorAll('a');
        links.forEach(link => {
            link.style.backgroundColor = '#ffff00';
            link.style.border = '2px solid #ff0000';
            link.style.borderRadius = '3px';
            link.style.transition = 'all 0.3s ease';
        });
        return links.length;
    }

    // 隐藏所有图片
    function hideAllImages() {
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            img.style.display = 'none';
        });
        return images.length;
    }

    // 处理来自popup的消息
    function handleMessage(request, sendResponse) {
        switch (request.action) {
            case 'ping':
                sendResponse({ status: 'ok' });
                break;
            default:
                sendResponse({ status: 'unknown action' });
        }
    }

    // 处理键盘快捷键
    function handleKeyboardShortcuts(e) {
        // Ctrl + Shift + H: 高亮链接
        if (e.ctrlKey && e.shiftKey && e.key === 'H') {
            e.preventDefault();
            highlightAllLinks();
            showToast('快捷键：已高亮所有链接');
        }
        
        // Ctrl + Shift + I: 隐藏图片
        if (e.ctrlKey && e.shiftKey && e.key === 'I') {
            e.preventDefault();
            hideAllImages();
            showToast('快捷键：已隐藏所有图片');
        }
        
        // Ctrl + Shift + T: 切换工具栏
        if (e.ctrlKey && e.shiftKey && e.key === 'T') {
            e.preventDefault();
            if (floatingToolbar) {
                floatingToolbar.style.display = floatingToolbar.style.display === 'none' ? 'block' : 'none';
            }
        }
    }

    // 显示提示消息
    function showToast(message, duration = 3000) {
        const toast = document.createElement('div');
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 50px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 6px;
            z-index: 10001;
            font-size: 14px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            animation: slideInRight 0.3s ease;
        `;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.remove();
        }, duration);
    }

    // 添加CSS动画
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        #web-helper-toolbar .toolbar-header {
            padding: 8px 12px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            border-radius: 8px 8px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            font-weight: 600;
        }
        
        #web-helper-toolbar .close-btn {
            background: none;
            border: none;
            font-size: 16px;
            cursor: pointer;
            padding: 0;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        #web-helper-toolbar .toolbar-content {
            padding: 8px;
        }
        
        #web-helper-toolbar .tool-btn {
            display: block;
            width: 100%;
            padding: 6px 8px;
            margin-bottom: 4px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background-color 0.2s;
        }
        
        #web-helper-toolbar .tool-btn:hover {
            background: #0056b3;
        }
        
        #web-helper-toolbar .tool-btn:last-child {
            margin-bottom: 0;
        }
    `;
    document.head.appendChild(style);

})();
