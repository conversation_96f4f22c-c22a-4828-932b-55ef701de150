* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 350px;
    min-height: 500px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
}

.container {
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 20px;
}

header h1 {
    color: white;
    font-size: 24px;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.feature-section {
    background: white;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.feature-section h3 {
    color: #4a5568;
    font-size: 16px;
    margin-bottom: 12px;
    font-weight: 600;
}

.button-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.action-btn, .info-btn, .custom-btn, .clear-btn {
    padding: 10px 16px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn {
    background: #4299e1;
    color: white;
}

.action-btn:hover {
    background: #3182ce;
    transform: translateY(-1px);
}

.info-btn {
    background: #48bb78;
    color: white;
}

.info-btn:hover {
    background: #38a169;
    transform: translateY(-1px);
}

.custom-btn {
    background: #ed8936;
    color: white;
}

.custom-btn:hover {
    background: #dd6b20;
    transform: translateY(-1px);
}

.clear-btn {
    background: #f56565;
    color: white;
    font-size: 12px;
    padding: 8px 12px;
}

.clear-btn:hover {
    background: #e53e3e;
    transform: translateY(-1px);
}

.info-display {
    margin-top: 12px;
    padding: 12px;
    background: #f7fafc;
    border-radius: 6px;
    font-size: 12px;
    color: #4a5568;
    min-height: 40px;
    border-left: 4px solid #48bb78;
}

.custom-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

#customSelector {
    padding: 10px 12px;
    border: 2px solid #e2e8f0;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

#customSelector:focus {
    outline: none;
    border-color: #4299e1;
}

.settings-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.settings-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #4a5568;
    cursor: pointer;
}

.settings-group input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #4299e1;
}

/* 动画效果 */
.feature-section {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 400px) {
    body {
        width: 300px;
    }
    
    .container {
        padding: 16px;
    }
}
