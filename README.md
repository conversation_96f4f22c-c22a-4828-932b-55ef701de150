# 🚀 网页助手 Chrome 插件

一个功能强大的Chrome插件，帮助你在网页上执行各种实用操作。

## ✨ 主要功能

### 🎯 页面操作
- **高亮链接**: 一键高亮页面上的所有链接
- **隐藏图片**: 快速隐藏页面上的所有图片
- **提取文本**: 提取页面的文本内容
- **获取页面信息**: 显示页面标题、URL、链接数、图片数等信息

### 🛠️ 自定义操作
- **CSS选择器**: 使用自定义CSS选择器选中并高亮元素
- **浮动工具栏**: 页面右上角的可拖拽工具栏
- **快捷键支持**: 
  - `Ctrl + Shift + H`: 高亮所有链接
  - `Ctrl + Shift + I`: 隐藏所有图片
  - `Ctrl + Shift + T`: 切换工具栏显示

### ⚙️ 设置选项
- **自动高亮**: 自动在新页面高亮所有链接
- **数据存储**: 保存用户设置和偏好
- **右键菜单**: 集成到浏览器右键菜单

## 📦 安装方法

### 开发者模式安装
1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择本项目文件夹
6. 插件安装完成！

### 构建项目（可选）
如果你想修改TypeScript代码：

```bash
# 安装依赖
npm install

# 构建项目
npm run build
```

## 🎮 使用方法

### 1. 弹窗界面
点击浏览器工具栏中的插件图标，打开功能面板：
- 使用各种页面操作按钮
- 查看页面信息
- 执行自定义CSS选择器操作
- 调整设置选项

### 2. 浮动工具栏
每个网页右上角都会显示一个可拖拽的浮动工具栏：
- 点击工具栏中的按钮快速执行操作
- 拖拽工具栏到任意位置
- 点击"收起"按钮最小化工具栏

### 3. 右键菜单
在网页上右键点击，选择"网页助手"菜单：
- 高亮所有链接
- 隐藏所有图片
- 提取页面文本
- 复制链接地址

### 4. 键盘快捷键
- `Ctrl + Shift + H`: 高亮所有链接
- `Ctrl + Shift + I`: 隐藏所有图片
- `Ctrl + Shift + T`: 切换工具栏显示

## 📁 项目结构

```
auto-update/
├── manifest.json          # Chrome插件配置文件
├── popup.html             # 弹窗界面HTML
├── popup.css              # 弹窗界面样式
├── popup.js               # 弹窗界面逻辑
├── content.js             # 内容脚本（注入到网页）
├── background.js          # 后台脚本
├── styles.css             # 注入到网页的样式
├── icons/                 # 插件图标文件夹
├── src/                   # TypeScript源码
├── package.json           # 项目配置
├── tsconfig.json          # TypeScript配置
└── README.md              # 说明文档
```

## 🔧 开发说明

### 技术栈
- **Manifest V3**: 最新的Chrome插件API
- **TypeScript**: 类型安全的JavaScript
- **Vanilla JS**: 原生JavaScript，无框架依赖
- **CSS3**: 现代CSS特性和动画

### 核心文件说明
- `manifest.json`: 定义插件权限、页面、脚本等配置
- `popup.*`: 点击插件图标时显示的弹窗界面
- `content.js`: 注入到每个网页中的脚本，提供页面交互功能
- `background.js`: 后台服务工作者，处理插件生命周期和跨页面通信
- `styles.css`: 注入到网页中的样式，确保插件UI不受页面样式影响

### 权限说明
- `activeTab`: 访问当前活动标签页
- `storage`: 存储用户设置和数据
- `scripting`: 向页面注入脚本
- `host_permissions`: 在所有网站上运行

## 🚀 功能扩展

你可以轻松扩展这个插件的功能：

1. **添加新的页面操作**: 在`popup.js`和`content.js`中添加新的功能函数
2. **自定义样式**: 修改`popup.css`和`styles.css`来改变界面外观
3. **增加快捷键**: 在`content.js`中的`handleKeyboardShortcuts`函数中添加新的快捷键
4. **扩展右键菜单**: 在`background.js`中添加新的右键菜单项

## 📝 许可证

本项目采用 MIT 许可证。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

---

**享受使用网页助手插件！** 🎉
