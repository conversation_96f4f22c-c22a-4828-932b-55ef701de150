<!DOCTYPE html>
<html>
<head>
    <title>图标生成器</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .icon-preview { margin: 20px 0; }
        canvas { border: 1px solid #ccc; margin: 10px; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>🚀 网页助手图标生成器</h1>
    <p>点击下面的按钮生成并下载图标文件：</p>
    
    <div class="icon-preview">
        <h3>图标预览：</h3>
        <canvas id="preview" width="128" height="128"></canvas>
    </div>
    
    <div>
        <button onclick="downloadIcon(16)">下载 16x16 图标</button>
        <button onclick="downloadIcon(32)">下载 32x32 图标</button>
        <button onclick="downloadIcon(48)">下载 48x48 图标</button>
        <button onclick="downloadIcon(128)">下载 128x128 图标</button>
    </div>
    
    <div>
        <button onclick="downloadAll()">下载所有图标</button>
    </div>

    <script>
        // 绘制图标
        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            canvas.width = size;
            canvas.height = size;
            
            // 清除画布
            ctx.clearRect(0, 0, size, size);
            
            // 背景渐变
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            
            // 绘制圆形背景
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - 2, 0, 2 * Math.PI);
            ctx.fill();
            
            // 绘制火箭图标
            ctx.fillStyle = 'white';
            ctx.font = `${size * 0.6}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('🚀', size/2, size/2);
        }
        
        // 预览图标
        function previewIcon() {
            const canvas = document.getElementById('preview');
            drawIcon(canvas, 128);
        }
        
        // 下载单个图标
        function downloadIcon(size) {
            const canvas = document.createElement('canvas');
            drawIcon(canvas, size);
            
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `icon${size}.png`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            });
        }
        
        // 下载所有图标
        function downloadAll() {
            const sizes = [16, 32, 48, 128];
            sizes.forEach((size, index) => {
                setTimeout(() => downloadIcon(size), index * 500);
            });
        }
        
        // 页面加载时显示预览
        window.onload = previewIcon;
    </script>
</body>
</html>
