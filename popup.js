// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', function() {
    // 获取所有按钮元素
    const highlightLinksBtn = document.getElementById('highlightLinks');
    const removeImagesBtn = document.getElementById('removeImages');
    const extractTextBtn = document.getElementById('extractText');
    const getPageInfoBtn = document.getElementById('getPageInfo');
    const customActionBtn = document.getElementById('customAction');
    const clearStorageBtn = document.getElementById('clearStorage');
    const autoHighlightCheckbox = document.getElementById('autoHighlight');
    const customSelector = document.getElementById('customSelector');
    const pageInfoDiv = document.getElementById('pageInfo');

    // 加载保存的设置
    loadSettings();

    // 高亮所有链接
    highlightLinksBtn.addEventListener('click', async () => {
        await executeContentScript('highlightLinks');
        showNotification('已高亮所有链接！');
    });

    // 隐藏图片
    removeImagesBtn.addEventListener('click', async () => {
        await executeContentScript('removeImages');
        showNotification('已隐藏所有图片！');
    });

    // 提取文本
    extractTextBtn.addEventListener('click', async () => {
        const result = await executeContentScript('extractText');
        if (result && result[0] && result[0].result) {
            pageInfoDiv.innerHTML = `<strong>提取的文本：</strong><br>${result[0].result.substring(0, 200)}...`;
        }
    });

    // 获取页面信息
    getPageInfoBtn.addEventListener('click', async () => {
        const result = await executeContentScript('getPageInfo');
        if (result && result[0] && result[0].result) {
            const info = result[0].result;
            pageInfoDiv.innerHTML = `
                <strong>页面信息：</strong><br>
                标题: ${info.title}<br>
                URL: ${info.url}<br>
                链接数: ${info.linkCount}<br>
                图片数: ${info.imageCount}
            `;
        }
    });

    // 自定义操作
    customActionBtn.addEventListener('click', async () => {
        const selector = customSelector.value.trim();
        if (!selector) {
            showNotification('请输入CSS选择器！', 'error');
            return;
        }
        
        const result = await executeContentScript('customAction', { selector });
        if (result && result[0] && result[0].result) {
            showNotification(`找到 ${result[0].result} 个元素并执行了操作！`);
        }
    });

    // 清除存储
    clearStorageBtn.addEventListener('click', () => {
        chrome.storage.local.clear(() => {
            showNotification('数据已清除！');
            autoHighlightCheckbox.checked = false;
        });
    });

    // 自动高亮设置
    autoHighlightCheckbox.addEventListener('change', () => {
        const isChecked = autoHighlightCheckbox.checked;
        chrome.storage.local.set({ autoHighlight: isChecked });
        showNotification(isChecked ? '已开启自动高亮' : '已关闭自动高亮');
    });

    // 执行内容脚本
    async function executeContentScript(action, params = {}) {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            const result = await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                func: contentScriptFunction,
                args: [action, params]
            });
            
            return result;
        } catch (error) {
            console.error('执行脚本失败:', error);
            showNotification('操作失败，请刷新页面后重试！', 'error');
        }
    }

    // 内容脚本函数（注入到页面中执行）
    function contentScriptFunction(action, params) {
        switch (action) {
            case 'highlightLinks':
                const links = document.querySelectorAll('a');
                links.forEach(link => {
                    link.style.backgroundColor = '#ffff00';
                    link.style.border = '2px solid #ff0000';
                    link.style.borderRadius = '3px';
                });
                return links.length;

            case 'removeImages':
                const images = document.querySelectorAll('img');
                images.forEach(img => {
                    img.style.display = 'none';
                });
                return images.length;

            case 'extractText':
                const textContent = document.body.innerText;
                return textContent.replace(/\s+/g, ' ').trim();

            case 'getPageInfo':
                return {
                    title: document.title,
                    url: window.location.href,
                    linkCount: document.querySelectorAll('a').length,
                    imageCount: document.querySelectorAll('img').length
                };

            case 'customAction':
                const elements = document.querySelectorAll(params.selector);
                elements.forEach(el => {
                    el.style.outline = '3px solid #00ff00';
                    el.style.backgroundColor = 'rgba(0, 255, 0, 0.1)';
                });
                return elements.length;

            default:
                return null;
        }
    }

    // 加载设置
    function loadSettings() {
        chrome.storage.local.get(['autoHighlight'], (result) => {
            autoHighlightCheckbox.checked = result.autoHighlight || false;
        });
    }

    // 显示通知
    function showNotification(message, type = 'success') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            padding: 10px 15px;
            border-radius: 6px;
            color: white;
            font-size: 12px;
            z-index: 10000;
            animation: slideInRight 0.3s ease;
            background: ${type === 'error' ? '#f56565' : '#48bb78'};
        `;

        document.body.appendChild(notification);

        // 3秒后自动移除
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
});
